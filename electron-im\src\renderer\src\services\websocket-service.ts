import { simpleim } from '../proto/message.js'

// 导入生成的类型
type MessageType = simpleim.MessageType
type IIMMessage = simpleim.IIMMessage
type IHeartbeat = simpleim.IHeartbeat
type ITextMessage = simpleim.ITextMessage
type IErrorMessage = simpleim.IErrorMessage

// WebSocket连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting'
}

// 事件类型
export interface WebSocketEvents {
  onConnect: () => void
  onDisconnect: (reason: string) => void
  onMessage: (message: simpleim.IMMessage) => void
  onError: (error: Error) => void
  onStateChange: (state: ConnectionState) => void
}

export class WebSocketService {
  private ws: WebSocket | null = null
  private url: string
  private token: string = ''
  private state: ConnectionState = ConnectionState.DISCONNECTED
  private heartbeatInterval: number | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private events: Partial<WebSocketEvents> = {}

  constructor(url: string) {
    this.url = url
  }

  /**
   * 设置JWT token
   */
  setToken(token: string): void {
    this.token = token
  }

  /**
   * 注册事件监听器
   */
  on<K extends keyof WebSocketEvents>(event: K, callback: WebSocketEvents[K]): void {
    this.events[event] = callback
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.state === ConnectionState.CONNECTED) {
        resolve()
        return
      }

      this.setState(ConnectionState.CONNECTING)

      try {
        this.ws = new WebSocket(this.url)
        this.ws.binaryType = 'arraybuffer'

        this.ws.onopen = () => {
          this.setState(ConnectionState.CONNECTED)
          this.reconnectAttempts = 0
          this.startHeartbeat()
          this.events.onConnect?.()
          resolve()
        }

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data)
        }

        this.ws.onclose = (event) => {
          this.handleDisconnect(`Connection closed: ${event.code} ${event.reason}`)
        }

        this.ws.onerror = (event) => {
          const error = new Error('WebSocket error')
          this.events.onError?.(error)
          reject(error)
        }
      } catch (error) {
        this.setState(ConnectionState.DISCONNECTED)
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.setState(ConnectionState.DISCONNECTED)
  }

  /**
   * 发送消息
   */
  sendMessage(message: IIMMessage): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.isConnected()) {
        reject(new Error('WebSocket not connected'))
        return
      }

      try {
        // 添加token到消息中
        const messageWithToken: IIMMessage = {
          ...message,
          token: this.token,
          timestamp: message.timestamp || Date.now()
        }

        // 序列化消息为二进制数据
        const buffer = simpleim.IMMessage.encode(messageWithToken).finish()

        // 发送二进制数据
        this.ws!.send(buffer)
        resolve()
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 发送心跳消息
   */
  sendHeartbeat(): Promise<void> {
    const heartbeat: IHeartbeat = {
      timestamp: Date.now(),
      status: 'online'
    }

    const message: IIMMessage = {
      type: simpleim.MessageType.HEARTBEAT,
      heartbeat
    }

    return this.sendMessage(message)
  }

  /**
   * 发送文本消息
   */
  sendTextMessage(textMessage: ITextMessage): Promise<void> {
    const message: IIMMessage = {
      type: simpleim.MessageType.TEXT_MESSAGE,
      textMessage
    }

    return this.sendMessage(message)
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.state === ConnectionState.CONNECTED && this.ws?.readyState === WebSocket.OPEN
  }

  /**
   * 获取当前连接状态
   */
  getState(): ConnectionState {
    return this.state
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: ArrayBuffer): void {
    try {
      // 反序列化二进制数据
      const uint8Array = new Uint8Array(data)
      const message = simpleim.IMMessage.decode(uint8Array)

      this.events.onMessage?.(message)
    } catch (error) {
      console.error('Failed to decode message:', error)
      this.events.onError?.(error as Error)
    }
  }

  /**
   * 处理断开连接
   */
  private handleDisconnect(reason: string): void {
    this.stopHeartbeat()
    this.setState(ConnectionState.DISCONNECTED)
    this.events.onDisconnect?.(reason)

    // 自动重连
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnect()
    }
  }

  /**
   * 重连
   */
  private reconnect(): void {
    this.setState(ConnectionState.RECONNECTING)
    this.reconnectAttempts++

    setTimeout(() => {
      this.connect().catch(() => {
        // 重连失败，继续尝试
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnect()
        }
      })
    }, this.reconnectDelay * this.reconnectAttempts)
  }

  /**
   * 设置连接状态
   */
  private setState(state: ConnectionState): void {
    if (this.state !== state) {
      this.state = state
      this.events.onStateChange?.(state)
    }
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    this.heartbeatInterval = window.setInterval(() => {
      this.sendHeartbeat().catch((error) => {
        console.error('Failed to send heartbeat:', error)
      })
    }, 25000) // 25秒发送一次心跳，避免30秒超时
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }
}
