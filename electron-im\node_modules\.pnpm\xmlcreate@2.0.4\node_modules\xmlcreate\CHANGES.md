## 2.0.4 ##

* Update dependencies
* Bug fixes

## 2.0.3 ##

* Bug fixes

## 2.0.2 ##

* Update dependencies
* Use ESLint instead of TSLint
* Use npm instead of gulp

## 2.0.1 ##

* Bug fixes

## 2.0.0 ##

* API rewrite and simplification
* Add option to use self-closing tags if an element is empty
* Stop indenting multi-line strings
* Add option to automatically fix strings that are not valid XML in certain
  cases
* More detailed error messages
* Other bug fixes

## 1.0.2 ##

* Bug fixes

## 1.0.1 ##

* Documentation fixes

## 1.0.0 ##

* Add null and undefined in type declarations
* Replace `XmlText` with `XmlCharData` and `XmlAttributeText` to reflect
  differences in what characters are acceptable in attribute values versus
  character data

## 0.1.1 ##

* Re-write in TypeScript
* Remove expensive dependencies (especially babel-runtime)
* Bug fixes and documentation fixes

## 0.1.0 ##

* Initial release
