import protobuf from 'protobufjs'

// Proto文件内容
const protoSource = `
syntax = "proto3";

package simpleim;

// 消息类型
enum MessageType {
  HEARTBEAT = 0;
  TEXT_MESSAGE = 1;
  ERROR = 100;
}

// 心跳消息（可扩展）
message Heartbeat {
  int64 timestamp = 1;   // 客户端发起心跳的时间戳（毫秒）
  string status = 2;    // 状态，当前暂定为 "online"
}

// 统一消息（支持文本和表情混合）
message TextMessage {
  string id = 1;
  string senderId = 2;
  string receiverId = 3;
  string content = 4;  // 支持文本
  int64 timestamp = 5; // 客户端生成或服务端生成的消息时间戳
}

// 错误消息
message ErrorMessage {
  string code = 1;
  string message = 2;
}

// 主消息包装器
message IMMessage {
  MessageType type = 1;   // 消息类型
  string messageId = 2;   // 唯一ID（可选，心跳可为空），新发送消息可不传。
  int64 timestamp = 3;    // 消息发送时间戳（统一），新发消息可不传
  string token = 4;       // JWT token字段，用于身份验证，通过登录获得

  oneof payload {
    Heartbeat heartbeat = 5; // 注意：超过30s没有上报心跳，服务端将关闭链接
    TextMessage textMessage = 6;
    ErrorMessage errorMessage = 100;
  }
}
`

// 消息类型枚举
export enum MessageType {
  HEARTBEAT = 0,
  TEXT_MESSAGE = 1,
  ERROR = 100
}

// TypeScript 接口定义
export interface IHeartbeat {
  timestamp: number
  status: string
}

export interface ITextMessage {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
}

export interface IErrorMessage {
  code: string
  message: string
}

export interface IIMMessage {
  type: MessageType
  messageId?: string
  timestamp?: number
  token?: string
  heartbeat?: IHeartbeat
  textMessage?: ITextMessage
  errorMessage?: IErrorMessage
}

// Proto消息类型
export interface ProtoTypes {
  IMMessage: protobuf.Type
  Heartbeat: protobuf.Type
  TextMessage: protobuf.Type
  ErrorMessage: protobuf.Type
  MessageType: protobuf.Enum
}

let protoTypes: ProtoTypes | null = null

/**
 * 初始化并加载proto定义
 */
export async function initProto(): Promise<ProtoTypes> {
  if (protoTypes) {
    return protoTypes
  }

  try {
    const root = protobuf.parse(protoSource).root
    
    protoTypes = {
      IMMessage: root.lookupType('simpleim.IMMessage'),
      Heartbeat: root.lookupType('simpleim.Heartbeat'),
      TextMessage: root.lookupType('simpleim.TextMessage'),
      ErrorMessage: root.lookupType('simpleim.ErrorMessage'),
      MessageType: root.lookupEnum('simpleim.MessageType')
    }

    return protoTypes
  } catch (error) {
    console.error('Failed to initialize proto:', error)
    throw error
  }
}

/**
 * 获取已初始化的proto类型
 */
export function getProtoTypes(): ProtoTypes {
  if (!protoTypes) {
    throw new Error('Proto types not initialized. Call initProto() first.')
  }
  return protoTypes
}
