<template>
  <div class="websocket-chat">
    <div class="chat-header">
      <h3>WebSocket + Protobuf Chat</h3>
      <div class="connection-status" :class="connectionState">
        {{ connectionState.toUpperCase() }}
      </div>
    </div>

    <div class="chat-controls">
      <input
        v-model="serverUrl"
        placeholder="WebSocket Server URL"
        :disabled="isConnected"
        class="input-field"
      />
      <input
        v-model="jwtToken"
        placeholder="JWT Token"
        type="password"
        :disabled="isConnected"
        class="input-field"
      />
      <button
        @click="toggleConnection"
        :disabled="connectionState === 'connecting' || connectionState === 'reconnecting'"
        class="btn"
        :class="isConnected ? 'btn-danger' : 'btn-primary'"
      >
        {{ isConnected ? 'Disconnect' : 'Connect' }}
      </button>
    </div>

    <div class="chat-messages" ref="messagesContainer">
      <div v-for="msg in messages" :key="msg.id" class="message" :class="msg.type">
        <div class="message-header">
          <span class="message-type">{{ msg.type }}</span>
          <span class="message-time">{{ msg.time }}</span>
        </div>
        <div class="message-content">{{ msg.content }}</div>
      </div>
    </div>

    <div class="chat-input" v-if="isConnected">
      <input v-model="receiverId" placeholder="Receiver ID" class="input-field" />
      <input
        v-model="messageText"
        placeholder="Type your message..."
        @keyup.enter="sendMessage"
        class="input-field flex-1"
      />
      <button @click="sendMessage" :disabled="!messageText.trim()" class="btn btn-primary">
        Send
      </button>
      <button @click="sendHeartbeat" class="btn btn-secondary">💓 Heartbeat</button>
    </div>

    <div class="chat-stats" v-if="isConnected">
      <div>Messages Sent: {{ stats.sent }}</div>
      <div>Messages Received: {{ stats.received }}</div>
      <div>Heartbeats: {{ stats.heartbeats }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { WebSocketService, ConnectionState } from '../services/websocket-service'
import { MessageUtils } from '../utils/message-utils'
import { simpleim } from '../proto/message.js'

// 响应式数据
const serverUrl = ref('ws://localhost:8080/ws')
const jwtToken = ref('')
const receiverId = ref('user456')
const messageText = ref('')
const connectionState = ref<ConnectionState>(ConnectionState.DISCONNECTED)
const messagesContainer = ref<HTMLElement>()

// 消息列表
interface ChatMessage {
  id: string
  type: string
  content: string
  time: string
  from?: string
  to?: string
}

const messages = ref<ChatMessage[]>([])

// 统计信息
const stats = reactive({
  sent: 0,
  received: 0,
  heartbeats: 0
})

// WebSocket服务实例
let wsService: WebSocketService | null = null

// 计算属性
const isConnected = computed(() => connectionState.value === ConnectionState.CONNECTED)

// 初始化WebSocket服务
function initWebSocketService() {
  if (wsService) {
    wsService.disconnect()
  }

  wsService = new WebSocketService(serverUrl.value)

  // 设置事件监听器
  wsService.on('onConnect', () => {
    addSystemMessage('Connected to server', 'success')
  })

  wsService.on('onDisconnect', (reason) => {
    addSystemMessage(`Disconnected: ${reason}`, 'error')
  })

  wsService.on('onMessage', (message) => {
    handleIncomingMessage(message)
    stats.received++
  })

  wsService.on('onError', (error) => {
    addSystemMessage(`Error: ${error.message}`, 'error')
  })

  wsService.on('onStateChange', (state) => {
    connectionState.value = state
  })
}

// 切换连接状态
async function toggleConnection() {
  if (!wsService) {
    initWebSocketService()
  }

  if (isConnected.value) {
    wsService!.disconnect()
  } else {
    try {
      wsService!.setToken(jwtToken.value)
      await wsService!.connect()
    } catch (error) {
      addSystemMessage(`Connection failed: ${error}`, 'error')
    }
  }
}

// 发送消息
async function sendMessage() {
  if (!messageText.value.trim() || !wsService || !isConnected.value) {
    return
  }

  try {
    const textMessage = MessageUtils.createTextMessage(
      'current-user',
      receiverId.value,
      messageText.value
    )

    await wsService.sendTextMessage(textMessage.textMessage!)

    // 添加到消息列表
    addChatMessage({
      id: textMessage.textMessage!.id!,
      type: 'sent',
      content: messageText.value,
      time: new Date().toLocaleTimeString(),
      to: receiverId.value
    })

    messageText.value = ''
    stats.sent++
  } catch (error) {
    addSystemMessage(`Failed to send message: ${error}`, 'error')
  }
}

// 发送心跳
async function sendHeartbeat() {
  if (!wsService || !isConnected.value) {
    return
  }

  try {
    await wsService.sendHeartbeat()
    addSystemMessage('Heartbeat sent', 'info')
    stats.heartbeats++
  } catch (error) {
    addSystemMessage(`Failed to send heartbeat: ${error}`, 'error')
  }
}

// 处理接收到的消息
function handleIncomingMessage(message: simpleim.IMMessage) {
  if (MessageUtils.isTextMessage(message)) {
    const textMsg = message.textMessage!
    addChatMessage({
      id: textMsg.id!,
      type: 'received',
      content: textMsg.content!,
      time: MessageUtils.formatTimestamp(textMsg.timestamp.toString()),
      from: textMsg.senderId
    })
  } else if (MessageUtils.isHeartbeat(message)) {
    const heartbeat = message.heartbeat!
    addSystemMessage(`Heartbeat received: ${heartbeat.status}`, 'info')
  } else if (MessageUtils.isErrorMessage(message)) {
    const errorMsg = message.errorMessage!
    addSystemMessage(`Server error ${errorMsg.code}: ${errorMsg.message}`, 'error')
  }
}

// 添加聊天消息
function addChatMessage(message: ChatMessage) {
  messages.value.push(message)
  scrollToBottom()
}

// 添加系统消息
function addSystemMessage(content: string, type: string) {
  addChatMessage({
    id: `system-${Date.now()}`,
    type: `system-${type}`,
    content,
    time: new Date().toLocaleTimeString()
  })
}

// 滚动到底部
function scrollToBottom() {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 生命周期
onMounted(() => {
  // 可以在这里初始化一些默认值
})

onUnmounted(() => {
  if (wsService) {
    wsService.disconnect()
  }
})
</script>

<style scoped>
.websocket-chat {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.connection-status {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.connection-status.connected {
  background-color: #d4edda;
  color: #155724;
}

.connection-status.connecting,
.connection-status.reconnecting {
  background-color: #fff3cd;
  color: #856404;
}

.connection-status.disconnected {
  background-color: #f8d7da;
  color: #721c24;
}

.chat-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.chat-messages {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
  margin-bottom: 20px;
  background-color: #f9f9f9;
}

.message {
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 4px;
}

.message.sent {
  background-color: #e3f2fd;
  margin-left: 20%;
}

.message.received {
  background-color: #f1f8e9;
  margin-right: 20%;
}

.message.system-info {
  background-color: #fff3e0;
  text-align: center;
  font-style: italic;
}

.message.system-error {
  background-color: #ffebee;
  text-align: center;
  color: #c62828;
}

.message.system-success {
  background-color: #e8f5e8;
  text-align: center;
  color: #2e7d32;
}

.message-header {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.message-content {
  font-size: 14px;
}

.chat-input {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.chat-stats {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #666;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.input-field {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.flex-1 {
  flex: 1;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}
</style>
