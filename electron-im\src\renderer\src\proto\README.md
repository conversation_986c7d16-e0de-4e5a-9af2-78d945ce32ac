# WebSocket + Protobuf 集成使用指南

本项目集成了 WebSocket 和 Protocol Buffers，用于高效的二进制消息传输。

## 文件结构

```
src/renderer/src/
├── proto/
│   ├── message.proto          # Protobuf 定义文件
│   ├── message.js            # 编译生成的 ES6 模块
│   ├── message.d.ts          # TypeScript 类型定义
│   └── README.md             # 本文档
├── services/
│   └── websocket-service.ts  # WebSocket 服务类
├── utils/
│   └── message-utils.ts      # 消息处理工具
├── components/
│   └── WebSocketChat.vue     # Vue 聊天组件示例
└── examples/
    └── websocket-protobuf-example.ts  # 使用示例
```

## 编译 Proto 文件

使用以下命令编译 proto 文件为 ES6 模块：

```bash
# 生成 JavaScript 文件
pbjs -t static-module -w es6 -o src/renderer/src/proto/message.js src/renderer/src/proto/message.proto

# 生成 TypeScript 类型定义
pbts -o src/renderer/src/proto/message.d.ts src/renderer/src/proto/message.js
```

## 快速开始

### 1. 基本使用

```typescript
import { WebSocketService } from '../services/websocket-service'
import { MessageUtils } from '../utils/message-utils'

// 创建 WebSocket 服务
const wsService = new WebSocketService('ws://localhost:8080/ws')

// 设置事件监听器
wsService.on('onConnect', () => {
  console.log('Connected!')
})

wsService.on('onMessage', (message) => {
  console.log('Received:', message)
})

// 连接到服务器
wsService.setToken('your-jwt-token')
await wsService.connect()

// 发送文本消息
await wsService.sendTextMessage({
  id: 'msg123',
  senderId: 'user1',
  receiverId: 'user2',
  content: 'Hello World!',
  timestamp: Date.now()
})
```

### 2. 使用消息工具类

```typescript
import { MessageUtils } from '../utils/message-utils'

// 创建各种类型的消息
const heartbeat = MessageUtils.createHeartbeat('online')
const textMsg = MessageUtils.createTextMessage('user1', 'user2', 'Hello!')
const errorMsg = MessageUtils.createErrorMessage('AUTH_FAILED', 'Invalid token')

// 序列化和反序列化
const binary = MessageUtils.serialize(textMsg)
const decoded = MessageUtils.deserialize(binary)

// 消息验证
const validation = MessageUtils.validate(textMsg)
if (validation) {
  console.error('Invalid message:', validation)
}

// 获取消息大小
const size = MessageUtils.getSize(textMsg)
console.log(`Message size: ${size} bytes`)
```

### 3. 在 Vue 组件中使用

```vue
<template>
  <div>
    <WebSocketChat />
  </div>
</template>

<script setup lang="ts">
import WebSocketChat from '../components/WebSocketChat.vue'
</script>
```

## 消息类型

### 1. 心跳消息 (HEARTBEAT)

```typescript
interface IHeartbeat {
  timestamp: number  // 时间戳
  status: string     // 状态，如 "online"
}
```

### 2. 文本消息 (TEXT_MESSAGE)

```typescript
interface ITextMessage {
  id: string         // 消息ID
  senderId: string   // 发送者ID
  receiverId: string // 接收者ID
  content: string    // 消息内容
  timestamp: number  // 时间戳
}
```

### 3. 错误消息 (ERROR)

```typescript
interface IErrorMessage {
  code: string       // 错误代码
  message: string    // 错误信息
}
```

### 4. 主消息包装器

```typescript
interface IIMMessage {
  type: MessageType          // 消息类型
  messageId?: string         // 消息ID（可选）
  timestamp?: number         // 时间戳（可选）
  token?: string            // JWT token
  heartbeat?: IHeartbeat    // 心跳消息
  textMessage?: ITextMessage // 文本消息
  errorMessage?: IErrorMessage // 错误消息
}
```

## WebSocket 服务 API

### 连接管理

```typescript
// 连接到服务器
await wsService.connect()

// 断开连接
wsService.disconnect()

// 检查连接状态
const isConnected = wsService.isConnected()
const state = wsService.getState()
```

### 消息发送

```typescript
// 发送文本消息
await wsService.sendTextMessage(textMessage)

// 发送心跳
await wsService.sendHeartbeat()

// 发送自定义消息
await wsService.sendMessage(customMessage)
```

### 事件监听

```typescript
wsService.on('onConnect', () => {})
wsService.on('onDisconnect', (reason) => {})
wsService.on('onMessage', (message) => {})
wsService.on('onError', (error) => {})
wsService.on('onStateChange', (state) => {})
```

## 消息工具 API

### 消息创建

```typescript
MessageUtils.createHeartbeat(status)
MessageUtils.createTextMessage(senderId, receiverId, content, id?)
MessageUtils.createErrorMessage(code, message)
```

### 序列化/反序列化

```typescript
MessageUtils.serialize(message)      // 转为二进制
MessageUtils.deserialize(data)       // 从二进制解析
MessageUtils.toObject(message)       // 转为普通对象
MessageUtils.fromObject(obj)         // 从普通对象创建
```

### 消息检查

```typescript
MessageUtils.isHeartbeat(message)
MessageUtils.isTextMessage(message)
MessageUtils.isErrorMessage(message)
MessageUtils.validate(message)
```

### 工具方法

```typescript
MessageUtils.generateMessageId()     // 生成消息ID
MessageUtils.formatTimestamp(ts)     // 格式化时间戳
MessageUtils.getSize(message)        // 获取消息大小
MessageUtils.clone(message)          // 克隆消息
MessageUtils.equals(msg1, msg2)      // 比较消息
```

## 最佳实践

1. **连接管理**：使用自动重连机制，处理网络中断
2. **心跳机制**：定期发送心跳保持连接活跃
3. **错误处理**：妥善处理连接错误和消息解析错误
4. **消息验证**：发送前验证消息格式
5. **性能优化**：使用二进制传输减少带宽占用

## 注意事项

1. 服务器需要支持二进制 WebSocket 消息
2. 心跳间隔建议设置为 25 秒（避免 30 秒超时）
3. JWT token 需要在连接时设置
4. 消息大小有限制，大文件需要分片传输
5. 确保 proto 文件与服务端保持一致

## 故障排除

### 连接失败
- 检查 WebSocket URL 是否正确
- 确认服务器是否运行
- 验证 JWT token 是否有效

### 消息解析失败
- 确认 proto 文件版本一致
- 检查消息格式是否正确
- 验证二进制数据完整性

### 性能问题
- 监控消息大小和频率
- 使用消息压缩
- 优化心跳间隔
