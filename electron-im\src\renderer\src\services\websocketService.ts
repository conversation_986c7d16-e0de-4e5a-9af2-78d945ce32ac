// WebSocket 封装 (心跳, 重连, 消息处理) - 使用 Protobuf
import { API_CONFIG, APP_CONFIG } from '../config'
import { simpleim } from '../proto/message.js'
import { MessageUtils } from '../utils/message-utils'

// 消息类型枚举 - 保持向后兼容
export enum MessageType {
  TEXT_MESSAGE = 1,
  SYSTEM_MESSAGE = 2,
  HEARTBEAT = 3
}

// 兼容旧接口（用于事件回调）
export interface TextMessage {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
}

export interface SystemMessage {
  id: string
  content: string
  timestamp: number
}

export interface ErrorMessage {
  code: string
  message: string
}

// WebSocket 连接状态
export enum WebSocketState {
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  DISCONNECTED = 'DISCONNECTED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR'
}

// WebSocket 事件类型
export interface WebSocketEvents {
  onStateChange: (state: WebSocketState) => void
  onMessage: (message: TextMessage) => void
  onSystemMessage: (message: SystemMessage) => void
  onError: (error: ErrorMessage) => void
  onHeartbeat: () => void
}

// WebSocket 服务类 - 使用 Protobuf
export class WebSocketService {
  private ws: WebSocket | null = null
  private url: string
  private token: string | null = null
  private state: WebSocketState = WebSocketState.DISCONNECTED
  private heartbeatInterval: number | null = null
  private reconnectTimeout: number | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private heartbeatIntervalMs = 25000 // 25秒发送一次心跳
  private reconnectDelayMs = 3000 // 重连延迟
  private events: Partial<WebSocketEvents> = {}

  constructor(url?: string) {
    this.url = url || API_CONFIG.WS_URL
  }

  // 设置事件监听器
  on<K extends keyof WebSocketEvents>(event: K, callback: WebSocketEvents[K]): void {
    this.events[event] = callback
  }

  // 移除事件监听器
  off<K extends keyof WebSocketEvents>(event: K): void {
    delete this.events[event]
  }

  // 触发事件
  private emit<K extends keyof WebSocketEvents>(
    event: K,
    ...args: Parameters<WebSocketEvents[K]>
  ): void {
    const callback = this.events[event]
    if (callback) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(callback as any)(...args)
    }
  }

  // 设置状态
  private setState(newState: WebSocketState): void {
    if (this.state !== newState) {
      this.state = newState
      this.emit('onStateChange', newState)
      console.log(`WebSocket状态变更: ${newState}`)
    }
  }

  // 获取当前状态
  getState(): WebSocketState {
    return this.state
  }

  // 连接WebSocket
  connect(token?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 如果已经连接，直接返回
        if (this.state === WebSocketState.CONNECTED) {
          resolve()
          return
        }

        // 设置token
        this.token = token || localStorage.getItem(APP_CONFIG.TOKEN_KEY)
        if (!this.token) {
          reject(new Error('未找到认证token'))
          return
        }

        this.setState(WebSocketState.CONNECTING)

        // 创建WebSocket连接，将token作为URL参数传递
        const wsUrl = `${this.url}?token=${this.token}`
        this.ws = new WebSocket(wsUrl)
        this.ws.binaryType = 'arraybuffer' // 设置为二进制模式以接收Protobuf数据

        // 连接成功
        this.ws.onopen = () => {
          console.log('WebSocket连接成功')
          this.setState(WebSocketState.CONNECTED)
          this.reconnectAttempts = 0
          this.startHeartbeat()
          resolve()
        }

        // 接收消息
        this.ws.onmessage = (event) => {
          this.handleMessage(event.data)
        }

        // 连接关闭
        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭:', event.code, event.reason)
          this.setState(WebSocketState.DISCONNECTED)
          this.stopHeartbeat()

          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000) {
            this.attemptReconnect()
          }
        }

        // 连接错误
        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.setState(WebSocketState.ERROR)
          reject(new Error('WebSocket连接失败'))
        }
      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        this.setState(WebSocketState.ERROR)
        reject(error)
      }
    })
  }

  // 断开连接
  disconnect(): void {
    console.log('主动断开WebSocket连接')
    this.stopHeartbeat()
    this.stopReconnect()

    if (this.ws) {
      this.ws.close(1000, '用户主动断开')
      this.ws = null
    }

    this.setState(WebSocketState.DISCONNECTED)
  }

  // 处理接收到的消息 - 使用Protobuf解析
  private handleMessage(data: ArrayBuffer | string): void {
    try {
      let protoMessage: simpleim.IMMessage

      if (data instanceof ArrayBuffer) {
        // 二进制Protobuf数据
        const uint8Array = new Uint8Array(data)
        protoMessage = simpleim.IMMessage.decode(uint8Array)
      } else {
        // 如果是字符串，尝试解析为JSON（向后兼容）
        const jsonMessage = JSON.parse(data)
        // 将JSON转换为Protobuf格式
        protoMessage = this.convertJsonToProto(jsonMessage)
      }

      console.log('收到Protobuf消息:', protoMessage)

      // 根据消息类型处理
      switch (protoMessage.type) {
        case simpleim.MessageType.TEXT_MESSAGE: {
          if (protoMessage.textMessage) {
            const textMessage: TextMessage = {
              id: protoMessage.textMessage.id || '',
              senderId: protoMessage.textMessage.senderId || '',
              receiverId: protoMessage.textMessage.receiverId || '',
              content: protoMessage.textMessage.content || '',
              timestamp:
                typeof protoMessage.textMessage.timestamp === 'number'
                  ? protoMessage.textMessage.timestamp
                  : parseInt(protoMessage.textMessage.timestamp?.toString() || '0')
            }
            this.emit('onMessage', textMessage)
          }
          break
        }

        case simpleim.MessageType.ERROR: {
          if (protoMessage.errorMessage) {
            const systemMessage: SystemMessage = {
              id: protoMessage.messageId || '',
              content: `错误: ${protoMessage.errorMessage.message}`,
              timestamp:
                typeof protoMessage.timestamp === 'number'
                  ? protoMessage.timestamp
                  : parseInt(protoMessage.timestamp?.toString() || Date.now().toString())
            }
            this.emit('onSystemMessage', systemMessage)
          }
          break
        }

        case simpleim.MessageType.HEARTBEAT:
          this.emit('onHeartbeat')
          break

        default:
          console.warn('未知消息类型:', protoMessage.type)
      }
    } catch (error) {
      console.error('解析Protobuf消息失败:', error)
      this.emit('onError', { code: 'PARSE_ERROR', message: '消息解析失败' })
    }
  }

  // 将JSON消息转换为Protobuf格式（向后兼容）
  private convertJsonToProto(jsonMessage: any): simpleim.IMMessage {
    if (jsonMessage.type === MessageType.TEXT_MESSAGE && jsonMessage.textMessage) {
      return MessageUtils.createTextMessage(
        jsonMessage.textMessage.senderId,
        jsonMessage.textMessage.receiverId,
        jsonMessage.textMessage.content,
        jsonMessage.textMessage.id
      )
    } else if (jsonMessage.type === MessageType.HEARTBEAT) {
      return MessageUtils.createHeartbeat('online')
    } else {
      // 默认创建错误消息
      return MessageUtils.createErrorMessage('UNKNOWN_FORMAT', 'Unknown message format')
    }
  }

  // 发送Protobuf消息
  private sendProtoMessage(message: simpleim.IIMMessage): boolean {
    if (this.state !== WebSocketState.CONNECTED || !this.ws) {
      console.error('WebSocket未连接，无法发送消息')
      return false
    }

    try {
      // 添加token和时间戳
      const messageWithToken: simpleim.IIMMessage = {
        ...message,
        token: this.token || '',
        timestamp: message.timestamp || Date.now(),
        messageId: message.messageId || MessageUtils.generateMessageId()
      }

      // 序列化为二进制数据
      const buffer = simpleim.IMMessage.encode(messageWithToken).finish()

      // 发送二进制数据
      this.ws.send(buffer)
      console.log('发送Protobuf消息:', messageWithToken)
      return true
    } catch (error) {
      console.error('发送Protobuf消息失败:', error)
      return false
    }
  }

  // 发送心跳
  private sendHeartbeat(): void {
    const heartbeat = MessageUtils.createHeartbeat('online')
    this.sendProtoMessage(heartbeat)
  }

  // 发送文本消息
  sendTextMessage(receiverId: string, content: string): boolean {
    const currentUser = this.getCurrentUser()
    const senderId = currentUser?.id || 'unknown'

    const textMessage = MessageUtils.createTextMessage(senderId, receiverId, content)

    return this.sendProtoMessage(textMessage)
  }

  // 开始心跳
  private startHeartbeat(): void {
    this.stopHeartbeat()

    // 立即发送一次心跳
    this.sendHeartbeat()

    // 设置定时心跳
    this.heartbeatInterval = window.setInterval(() => {
      this.sendHeartbeat()
    }, this.heartbeatIntervalMs)

    console.log(`心跳已启动，间隔: ${this.heartbeatIntervalMs}ms`)
  }

  // 停止心跳
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
      console.log('心跳已停止')
    }
  }

  // 尝试重连
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('重连次数已达上限，停止重连')
      this.setState(WebSocketState.ERROR)
      return
    }

    this.reconnectAttempts++
    this.setState(WebSocketState.RECONNECTING)

    console.log(`尝试第${this.reconnectAttempts}次重连...`)

    this.reconnectTimeout = window.setTimeout(() => {
      this.connect(this.token || undefined).catch((error) => {
        console.error(`第${this.reconnectAttempts}次重连失败:`, error)
        this.attemptReconnect()
      })
    }, this.reconnectDelayMs)
  }

  // 停止重连
  private stopReconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
    this.reconnectAttempts = 0
  }

  // 生成消息ID
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 获取当前用户信息
  private getCurrentUser() {
    const userStr = localStorage.getItem(APP_CONFIG.USER_KEY)
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch {
        return null
      }
    }
    return null
  }

  // 检查连接状态
  isConnected(): boolean {
    return this.state === WebSocketState.CONNECTED
  }

  // 获取重连次数
  getReconnectAttempts(): number {
    return this.reconnectAttempts
  }
}

// 导出WebSocket服务实例
export const wsService = new WebSocketService()
